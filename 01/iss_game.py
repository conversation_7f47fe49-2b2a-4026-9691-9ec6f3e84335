"""
国际空间站追踪游戏
让学习Python网络编程变得更有趣！
"""

import requests
import json
from datetime import datetime
import time

class ISSTracker:
    def __init__(self):
        self.url = "http://api.open-notify.org/iss-now.json"
        self.score = 0
    
    def welcome(self):
        """欢迎界面"""
        print("🌟" * 20)
        print("🛰️  国际空间站追踪游戏")
        print("🌟" * 20)
        print("你是一名太空任务控制员！")
        print("你的任务是追踪国际空间站的位置")
        print("让我们开始学习如何与太空通信吧！\n")
    
    def quiz_status_code(self):
        """状态码小测验"""
        print("📚 知识小测验：HTTP状态码")
        print("当我们向服务器发送请求时，服务器会返回一个状态码")
        print("请选择正确答案：")
        print("A. 200 表示成功")
        print("B. 404 表示找不到")
        print("C. 500 表示服务器错误")
        print("D. 以上都对")
        
        answer = input("请输入你的答案 (A/B/C/D): ").upper()
        if answer == 'D':
            print("✅ 正确！你获得了10分！")
            self.score += 10
        else:
            print("❌ 不对哦，正确答案是D。所有选项都是正确的HTTP状态码含义。")
        print()
    
    def get_iss_position(self):
        """获取ISS位置的互动版本"""
        print("🚀 任务开始：联系国际空间站")
        print("正在发送信号到太空...")
        
        # 添加一些戏剧效果
        for i in range(3):
            print("📡 发送中" + "." * (i + 1))
            time.sleep(0.5)
        
        try:
            response = requests.get(self.url)
            
            print(f"\n📊 收到回应！状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 太棒了！成功连接到国际空间站！")
                self.score += 20
                return response
            else:
                print("❌ 连接失败，请稍后重试")
                return None
                
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            return None
    
    def analyze_data(self, response):
        """分析数据的互动版本"""
        if not response:
            return None
        
        print("\n🔍 分析从太空传回的数据...")
        print("原始数据长这样：")
        print("-" * 30)
        print(response.text)
        print("-" * 30)
        
        # 让学生猜测这是什么格式
        guess = input("你觉得这是什么数据格式？(提示：以J开头): ")
        if guess.upper().startswith('J'):
            print("✅ 正确！这是JSON格式！你获得了15分！")
            self.score += 15
        else:
            print("💡 这是JSON格式，一种常用的数据交换格式")
        
        try:
            data = json.loads(response.text)
            return data
        except:
            print("❌ 数据解析失败")
            return None
    
    def show_location(self, data):
        """显示位置信息"""
        if not data:
            return
        
        print(f"\n🌍 国际空间站当前位置报告：")
        lat = float(data['iss_position']['latitude'])
        lon = float(data['iss_position']['longitude'])
        
        print(f"纬度: {lat}°")
        print(f"经度: {lon}°")
        
        # 简单的位置描述
        if lat > 0:
            lat_desc = "北纬"
        else:
            lat_desc = "南纬"
        
        if lon > 0:
            lon_desc = "东经"
        else:
            lon_desc = "西经"
        
        print(f"位置描述: {lat_desc} {abs(lat):.1f}°, {lon_desc} {abs(lon):.1f}°")
        
        # 时间戳解释
        timestamp = data['timestamp']
        readable_time = datetime.fromtimestamp(timestamp)
        
        print(f"\n⏰ 数据时间戳: {timestamp}")
        print(f"这表示: {readable_time.strftime('%Y年%m月%d日 %H:%M:%S')}")
        
        self.score += 25
    
    def final_score(self):
        """显示最终得分"""
        print("\n🎉 任务完成！")
        print(f"你的总得分: {self.score} 分")
        
        if self.score >= 60:
            print("🏆 恭喜！你已经是一名合格的太空程序员了！")
        elif self.score >= 40:
            print("👍 不错！继续努力，你很快就能掌握这些技能！")
        else:
            print("💪 加油！多练习几次，你一定能做得更好！")
    
    def run_game(self):
        """运行游戏"""
        self.welcome()
        
        # 第一关：知识测验
        self.quiz_status_code()
        
        # 第二关：获取数据
        response = self.get_iss_position()
        
        # 第三关：分析数据
        data = self.analyze_data(response)
        
        # 第四关：显示位置
        self.show_location(data)
        
        # 显示最终得分
        self.final_score()

# 运行游戏
if __name__ == "__main__":
    game = ISSTracker()
    game.run_game()
