"""
国际空间站位置追踪器 - 基础版本
适合小学生学习 Python 网络请求和时间戳概念

学习目标：
1. 理解 requests.get() 的使用
2. 学会检查 response.status_code
3. 了解 response.text 的内容
4. 认识时间戳的概念
"""

import requests
import json
from datetime import datetime

def get_iss_position():
    """
    获取国际空间站的当前位置
    """
    print("🚀 正在联系国际空间站...")
    
    # 步骤1: 发送网络请求
    url = "http://api.open-notify.org/iss-now.json"
    response = requests.get(url)
    
    # 步骤2: 检查请求是否成功
    print(f"📡 服务器响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 成功连接到服务器!")
    else:
        print("❌ 连接失败!")
        return None
    
    # 步骤3: 查看原始响应内容
    print("\n📄 服务器返回的原始数据:")
    print(response.text)
    
    # 步骤4: 解析JSON数据
    try:
        data = json.loads(response.text)
        return data
    except json.JSONDecodeError:
        print("❌ 数据格式错误!")
        return None

def explain_timestamp(timestamp):
    """
    解释时间戳的概念
    """
    print(f"\n⏰ 时间戳解释:")
    print(f"时间戳数字: {timestamp}")
    print(f"这是从1970年1月1日开始计算的秒数")
    
    # 转换为人类可读的时间
    readable_time = datetime.fromtimestamp(timestamp)
    print(f"转换为北京时间: {readable_time.strftime('%Y年%m月%d日 %H:%M:%S')}")

def show_iss_location(data):
    """
    显示国际空间站的位置信息
    """
    if not data:
        return
    
    print(f"\n🌍 国际空间站当前位置:")
    print(f"纬度: {data['iss_position']['latitude']}°")
    print(f"经度: {data['iss_position']['longitude']}°")
    
    # 解释时间戳
    explain_timestamp(data['timestamp'])

def main():
    """
    主程序
    """
    print("=" * 50)
    print("🛰️  国际空间站位置追踪器")
    print("=" * 50)
    
    # 获取ISS位置数据
    iss_data = get_iss_position()
    
    # 显示位置信息
    show_iss_location(iss_data)
    
    print("\n" + "=" * 50)
    print("程序结束，感谢使用! 👋")

if __name__ == "__main__":
    main()
