"""
分步教学：国际空间站API学习
每一步都有详细解释，适合小学生跟着学习
"""

import requests
import json
from datetime import datetime

print("🎓 欢迎来到Python网络编程课堂!")
print("今天我们要学习如何获取国际空间站的位置\n")

# ==================== 第一步：了解API ====================
print("📚 第一步：什么是API?")
print("API就像一个服务员，我们告诉它我们想要什么信息，")
print("它就会去厨房(服务器)帮我们拿回来。")
print("国际空间站API的地址是: http://api.open-notify.org/iss-now.json")
input("按回车键继续...")

# ==================== 第二步：发送请求 ====================
print("\n🚀 第二步：使用 requests.get() 发送请求")
print("就像打电话一样，我们要先拨号码")

url = "http://api.open-notify.org/iss-now.json"
print(f"正在向 {url} 发送请求...")

response = requests.get(url)
print("请求已发送! 📡")
input("按回车键继续...")

# ==================== 第三步：检查状态码 ====================
print("\n📊 第三步：检查 response.status_code")
print("状态码就像服务员的回答：")
print("200 = '好的，我找到了你要的信息!'")
print("404 = '抱歉，我找不到你要的东西'")
print("500 = '厨房出问题了，请稍后再试'")

print(f"\n我们收到的状态码是: {response.status_code}")

if response.status_code == 200:
    print("✅ 太棒了！服务器成功找到了信息!")
else:
    print("❌ 哦不！出现了问题")
    exit()

input("按回车键继续...")

# ==================== 第四步：查看原始数据 ====================
print("\n📄 第四步：查看 response.text")
print("这是服务器返回给我们的原始数据，就像一封信的内容：")
print("-" * 40)
print(response.text)
print("-" * 40)
print("这种格式叫做JSON，就像一个有组织的信息盒子")
input("按回车键继续...")

# ==================== 第五步：解析数据 ====================
print("\n🔍 第五步：把JSON数据变成Python能理解的格式")
data = json.loads(response.text)
print("转换成功！现在我们可以轻松地获取信息了")

print(f"消息状态: {data['message']}")
print(f"时间戳: {data['timestamp']}")
print(f"纬度: {data['iss_position']['latitude']}")
print(f"经度: {data['iss_position']['longitude']}")
input("按回车键继续...")

# ==================== 第六步：理解时间戳 ====================
print("\n⏰ 第六步：什么是时间戳?")
timestamp = data['timestamp']
print(f"时间戳是: {timestamp}")
print("这个数字表示从1970年1月1日到现在经过了多少秒")
print("就像一个超级精确的计时器!")

# 转换为人类可读的时间
readable_time = datetime.fromtimestamp(timestamp)
print(f"转换成我们能理解的时间: {readable_time}")
input("按回车键继续...")

# ==================== 第七步：总结 ====================
print("\n🎉 恭喜你！你已经学会了：")
print("1. 使用 requests.get() 发送网络请求")
print("2. 检查 response.status_code 确认请求成功")
print("3. 读取 response.text 获取服务器返回的数据")
print("4. 理解时间戳的概念")
print("5. 获取国际空间站的实时位置!")

print(f"\n🛰️ 国际空间站现在在地球上的位置:")
print(f"纬度: {data['iss_position']['latitude']}° (南北位置)")
print(f"经度: {data['iss_position']['longitude']}° (东西位置)")
print(f"数据获取时间: {readable_time}")

print("\n🌟 你现在是一个小小的太空程序员了！")
