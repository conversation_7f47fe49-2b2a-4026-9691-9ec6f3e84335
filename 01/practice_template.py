"""
练习模板：国际空间站位置获取器
请按照注释提示完成代码，学习网络请求的基本概念

学习重点：
- requests.get()
- response.status_code  
- response.text
- 时间戳概念
"""

# 导入需要的库
import requests
import json
from datetime import datetime

def get_iss_data():
    """
    练习1: 获取国际空间站数据
    请完成以下步骤
    """
    print("🚀 开始获取国际空间站位置...")
    
    # TODO: 定义API的URL地址
    url = "_______________"  # 请填入正确的URL
    
    # TODO: 使用requests.get()发送请求
    response = requests._____(url)  # 请填入正确的方法
    
    # TODO: 检查状态码
    print(f"状态码: {response._______}")  # 请填入正确的属性
    
    # TODO: 判断请求是否成功 (状态码为200表示成功)
    if response._______ == 200:
        print("✅ 请求成功!")
        return response
    else:
        print("❌ 请求失败!")
        return None

def show_raw_data(response):
    """
    练习2: 显示原始数据
    """
    if response is None:
        return
    
    print("\n📄 服务器返回的原始数据:")
    # TODO: 打印response.text
    print(response._______)  # 请填入正确的属性

def parse_and_show_data(response):
    """
    练习3: 解析并显示数据
    """
    if response is None:
        return
    
    # TODO: 将JSON字符串转换为Python字典
    data = json.loads(response._______)  # 请填入正确的属性
    
    print("\n🌍 解析后的数据:")
    print(f"消息: {data['_______']}")  # 请填入正确的键名
    print(f"时间戳: {data['_______']}")  # 请填入正确的键名
    print(f"纬度: {data['_______']['_______']}")  # 请填入正确的键名
    print(f"经度: {data['_______']['_______']}")  # 请填入正确的键名
    
    return data

def explain_timestamp(data):
    """
    练习4: 时间戳转换
    """
    if data is None:
        return
    
    # TODO: 获取时间戳
    timestamp = data['_______']  # 请填入正确的键名
    
    print(f"\n⏰ 时间戳解释:")
    print(f"时间戳数字: {timestamp}")
    print("这个数字表示从1970年1月1日开始经过的秒数")
    
    # TODO: 将时间戳转换为可读时间
    readable_time = datetime.fromtimestamp(_______)  # 请填入时间戳变量
    print(f"转换为时间: {readable_time}")

def main():
    """
    主程序 - 把所有步骤组合起来
    """
    print("=" * 50)
    print("🛰️  国际空间站位置练习程序")
    print("=" * 50)
    
    # 步骤1: 获取数据
    response = get_iss_data()
    
    # 步骤2: 显示原始数据
    show_raw_data(response)
    
    # 步骤3: 解析并显示数据
    data = parse_and_show_data(response)
    
    # 步骤4: 解释时间戳
    explain_timestamp(data)
    
    print("\n🎉 练习完成!")

# 运行程序
if __name__ == "__main__":
    main()

"""
答案提示：
1. URL: "http://api.open-notify.org/iss-now.json"
2. 方法: get
3. 属性: status_code
4. 属性: text
5. 键名: "message", "timestamp", "iss_position", "latitude", "longitude"
6. 变量: timestamp
"""
