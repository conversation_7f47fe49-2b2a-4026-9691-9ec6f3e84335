"""
练习答案：国际空间站位置获取器
这是practice_template.py的完整答案版本
"""

import requests
import json
from datetime import datetime

def get_iss_data():
    """
    练习1答案: 获取国际空间站数据
    """
    print("🚀 开始获取国际空间站位置...")
    
    # 答案: 定义API的URL地址
    url = "http://api.open-notify.org/iss-now.json"
    
    # 答案: 使用requests.get()发送请求
    response = requests.get(url)
    
    # 答案: 检查状态码
    print(f"状态码: {response.status_code}")
    
    # 答案: 判断请求是否成功
    if response.status_code == 200:
        print("✅ 请求成功!")
        return response
    else:
        print("❌ 请求失败!")
        return None

def show_raw_data(response):
    """
    练习2答案: 显示原始数据
    """
    if response is None:
        return
    
    print("\n📄 服务器返回的原始数据:")
    # 答案: 打印response.text
    print(response.text)

def parse_and_show_data(response):
    """
    练习3答案: 解析并显示数据
    """
    if response is None:
        return
    
    # 答案: 将JSON字符串转换为Python字典
    data = json.loads(response.text)
    
    print("\n🌍 解析后的数据:")
    print(f"消息: {data['message']}")
    print(f"时间戳: {data['timestamp']}")
    print(f"纬度: {data['iss_position']['latitude']}")
    print(f"经度: {data['iss_position']['longitude']}")
    
    return data

def explain_timestamp(data):
    """
    练习4答案: 时间戳转换
    """
    if data is None:
        return
    
    # 答案: 获取时间戳
    timestamp = data['timestamp']
    
    print(f"\n⏰ 时间戳解释:")
    print(f"时间戳数字: {timestamp}")
    print("这个数字表示从1970年1月1日开始经过的秒数")
    
    # 答案: 将时间戳转换为可读时间
    readable_time = datetime.fromtimestamp(timestamp)
    print(f"转换为时间: {readable_time}")

def main():
    """
    主程序
    """
    print("=" * 50)
    print("🛰️  国际空间站位置练习程序 - 答案版本")
    print("=" * 50)
    
    response = get_iss_data()
    show_raw_data(response)
    data = parse_and_show_data(response)
    explain_timestamp(data)
    
    print("\n🎉 程序运行完成!")

if __name__ == "__main__":
    main()
