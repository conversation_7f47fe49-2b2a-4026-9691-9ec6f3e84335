# 国际空间站位置API教学项目

## 📚 教学目标

本项目通过国际空间站位置API学习Python网络编程基础概念：

- **requests.get()** - 发送HTTP请求
- **response.status_code** - 检查请求状态
- **response.text** - 获取响应内容
- **时间戳概念** - 理解时间的数字表示

## 🗂️ 文件说明

### 1. `iss_tracker_basic.py` - 基础完整示例
- 完整的功能实现
- 清晰的代码结构
- 适合演示使用

### 2. `step_by_step_tutorial.py` - 分步教学版本
- 逐步解释每个概念
- 互动式学习体验
- 需要学生按回车键继续
- **推荐作为课堂教学的主要工具**

### 3. `practice_template.py` - 练习模板
- 填空式练习
- 学生需要完成缺失的代码
- 包含提示和答案

### 4. `practice_answers.py` - 练习答案
- 完整的答案版本
- 供老师参考和学生对照

### 5. `iss_game.py` - 互动游戏版本
- 游戏化学习体验
- 积分系统
- 知识问答
- 增加学习趣味性

## 🚀 使用建议

### 课堂教学流程：

1. **导入阶段** (5分钟)
   - 介绍国际空间站
   - 解释什么是API
   - 运行 `step_by_step_tutorial.py` 进行演示

2. **概念讲解** (10分钟)
   - 重点讲解四个核心概念：
     - `requests.get()` - 就像打电话
     - `status_code` - 服务员的回答
     - `response.text` - 信件内容
     - 时间戳 - 超级计时器

3. **动手实践** (15分钟)
   - 学生使用 `practice_template.py` 完成练习
   - 老师巡回指导
   - 对照 `practice_answers.py` 检查答案

4. **趣味巩固** (10分钟)
   - 运行 `iss_game.py` 进行游戏化复习
   - 鼓励学生获得高分

## 🔧 环境准备

### 安装依赖：
```bash
pip install requests
```

### 运行示例：
```bash
# 分步教学版本（推荐）
python step_by_step_tutorial.py

# 基础示例
python iss_tracker_basic.py

# 互动游戏
python iss_game.py
```

## 📖 核心概念解释

### 1. requests.get()
```python
response = requests.get(url)
```
- 就像给服务器打电话
- 告诉服务器我们想要什么信息

### 2. response.status_code
```python
if response.status_code == 200:
    print("成功!")
```
- 200 = 成功
- 404 = 找不到
- 500 = 服务器错误

### 3. response.text
```python
print(response.text)
```
- 服务器返回的原始数据
- 通常是JSON格式

### 4. 时间戳
```python
timestamp = 1750643515
readable_time = datetime.fromtimestamp(timestamp)
```
- 从1970年1月1日开始计算的秒数
- 计算机用来记录时间的方式

## 🎯 教学重点

1. **循序渐进** - 从简单概念开始，逐步深入
2. **实际应用** - 使用真实的API数据
3. **互动体验** - 让学生参与每个步骤
4. **趣味性** - 通过太空主题增加兴趣
5. **实践导向** - 让学生动手编写代码

## 💡 扩展活动建议

1. **位置可视化** - 在地图上标出ISS位置
2. **数据记录** - 每隔几分钟记录一次位置
3. **轨道预测** - 讨论ISS的运行轨道
4. **其他API** - 尝试天气API、新闻API等

## 🔍 常见问题

**Q: 网络连接失败怎么办？**
A: 检查网络连接，或使用备用的本地JSON文件进行练习

**Q: 学生理解困难怎么办？**
A: 使用更多生活化的比喻，如"打电话"、"写信"等

**Q: 如何增加难度？**
A: 可以引入错误处理、数据存储、图形界面等概念

## 📞 技术支持

如果在教学过程中遇到问题，可以：
1. 检查网络连接
2. 确认Python环境正确安装
3. 查看错误信息并对照代码

祝教学愉快！🚀✨
